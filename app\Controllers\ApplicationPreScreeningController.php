<?php

namespace App\Controllers;

// Remove all model imports since we're using mock data

class ApplicationPreScreeningController extends BaseController
{
    // Remove model properties - replaced with mock data
    protected $session;

    public function __construct()
    {
        helper(['form', 'url', 'application']); // Assuming application_helper exists
        $this->session = \Config\Services::session();
        // Remove all model instantiations - using mock data instead
    }

    /**
     * Generate mock application data
     */
    private function getMockApplications()
    {
        return [
            [
                'id' => 1,
                'applicant_id' => 101,
                'position_id' => 201,
                'application_number' => 'APP-2024-001',
                'recieved_acknowledged' => '2024-01-15 10:30:00',
                'pre_screened' => null,
                'pre_screened_status' => null,
                'pre_screened_remarks' => null,
                'created_at' => '2024-01-10 08:00:00',
                'fname' => 'John',
                'lname' => 'Doe',
                'position_name' => 'Senior Software Engineer'
            ],
            [
                'id' => 2,
                'applicant_id' => 102,
                'position_id' => 202,
                'application_number' => 'APP-2024-002',
                'recieved_acknowledged' => '2024-01-16 14:20:00',
                'pre_screened' => '2024-01-17 16:00:00',
                'pre_screened_status' => 'passed',
                'pre_screened_remarks' => 'Meets all requirements',
                'created_at' => '2024-01-11 09:15:00',
                'fname' => 'Jane',
                'lname' => 'Smith',
                'position_name' => 'Project Manager'
            ],
            [
                'id' => 3,
                'applicant_id' => 103,
                'position_id' => 203,
                'application_number' => 'APP-2024-003',
                'recieved_acknowledged' => '2024-01-17 11:45:00',
                'pre_screened' => null,
                'pre_screened_status' => null,
                'pre_screened_remarks' => null,
                'created_at' => '2024-01-12 14:30:00',
                'fname' => 'Michael',
                'lname' => 'Johnson',
                'position_name' => 'Data Analyst'
            ],
            [
                'id' => 4,
                'applicant_id' => 104,
                'position_id' => 204,
                'application_number' => 'APP-2024-004',
                'recieved_acknowledged' => '2024-01-18 09:30:00',
                'pre_screened' => '2024-01-19 10:15:00',
                'pre_screened_status' => 'failed',
                'pre_screened_remarks' => 'Does not meet minimum education requirements',
                'created_at' => '2024-01-13 16:45:00',
                'fname' => 'Sarah',
                'lname' => 'Williams',
                'position_name' => 'Research Officer'
            ]
        ];
    }

    /**
     * Generate mock applicant data
     */
    private function getMockApplicant($applicantId)
    {
        $applicants = [
            101 => [
                'id' => 101,
                'unique_id' => 'APL-101',
                'email' => '<EMAIL>',
                'first_name' => 'John',
                'last_name' => 'Doe',
                'gender' => 'Male',
                'dobirth' => '1990-05-15',
                'place_of_origin' => 'New York',
                'contact_details' => '{"phone": "+1234567890", "address": "123 Main St"}',
                'citizenship' => 'American',
                'marital_status' => 'Single',
                'current_employer' => 'Tech Corp',
                'current_position' => 'Software Developer',
                'current_salary' => '$75,000'
            ],
            102 => [
                'id' => 102,
                'unique_id' => 'APL-102',
                'email' => '<EMAIL>',
                'first_name' => 'Jane',
                'last_name' => 'Smith',
                'gender' => 'Female',
                'dobirth' => '1988-09-22',
                'place_of_origin' => 'California',
                'contact_details' => '{"phone": "+1234567891", "address": "456 Oak Ave"}',
                'citizenship' => 'American',
                'marital_status' => 'Married',
                'current_employer' => 'Innovation Inc',
                'current_position' => 'Team Lead',
                'current_salary' => '$85,000'
            ],
            103 => [
                'id' => 103,
                'unique_id' => 'APL-103',
                'email' => '<EMAIL>',
                'first_name' => 'Michael',
                'last_name' => 'Johnson',
                'gender' => 'Male',
                'dobirth' => '1992-03-10',
                'place_of_origin' => 'Texas',
                'contact_details' => '{"phone": "+1234567892", "address": "789 Pine St"}',
                'citizenship' => 'American',
                'marital_status' => 'Single',
                'current_employer' => 'Data Solutions',
                'current_position' => 'Data Analyst',
                'current_salary' => '$68,000'
            ],
            104 => [
                'id' => 104,
                'unique_id' => 'APL-104',
                'email' => '<EMAIL>',
                'first_name' => 'Sarah',
                'last_name' => 'Williams',
                'gender' => 'Female',
                'dobirth' => '1985-11-08',
                'place_of_origin' => 'Florida',
                'contact_details' => '{"phone": "+1234567893", "address": "321 Elm Dr"}',
                'citizenship' => 'American',
                'marital_status' => 'Married',
                'current_employer' => 'Research Labs',
                'current_position' => 'Research Assistant',
                'current_salary' => '$58,000'
            ]
        ];

        return $applicants[$applicantId] ?? [];
    }

    /**
     * Generate mock application with detailed information
     */
    private function getMockApplicationDetails($id)
    {
        $applications = [
            1 => [
                'id' => 1,
                'applicant_id' => 101,
                'position_id' => 201,
                'designation' => 'Senior Software Engineer',
                'group_name' => 'Technology',
                'exercise_id' => 301,
                'exercise_name' => 'Tech Recruitment 2024',
                'pre_screen_criteria' => json_encode([
                    ['criterion' => 'Bachelor\'s degree in Computer Science or related field', 'required' => true],
                    ['criterion' => 'Minimum 5 years of programming experience', 'required' => true],
                    ['criterion' => 'Experience with modern web frameworks', 'required' => true],
                    ['criterion' => 'Knowledge of database systems', 'required' => false]
                ]),
                'application_number' => 'APP-2024-001',
                'first_name' => 'John',
                'last_name' => 'Doe',
                'created_at' => '2024-01-10 08:00:00',
                'recieved_acknowledged' => '2024-01-15 10:30:00',
                'pre_screened' => null,
                'pre_screened_status' => null,
                'pre_screened_remarks' => null
            ],
            2 => [
                'id' => 2,
                'applicant_id' => 102,
                'position_id' => 202,
                'designation' => 'Project Manager',
                'group_name' => 'Management',
                'exercise_id' => 302,
                'exercise_name' => 'Management Recruitment 2024',
                'pre_screen_criteria' => json_encode([
                    ['criterion' => 'Bachelor\'s degree in Business or related field', 'required' => true],
                    ['criterion' => 'Minimum 3 years of project management experience', 'required' => true],
                    ['criterion' => 'PMP certification preferred', 'required' => false],
                    ['criterion' => 'Leadership experience', 'required' => true]
                ]),
                'application_number' => 'APP-2024-002',
                'first_name' => 'Jane',
                'last_name' => 'Smith',
                'created_at' => '2024-01-11 09:15:00',
                'recieved_acknowledged' => '2024-01-16 14:20:00',
                'pre_screened' => '2024-01-17 16:00:00',
                'pre_screened_status' => 'passed',
                'pre_screened_remarks' => 'Meets all requirements'
            ],
            3 => [
                'id' => 3,
                'applicant_id' => 103,
                'position_id' => 203,
                'designation' => 'Data Analyst',
                'group_name' => 'Analytics',
                'exercise_id' => 303,
                'exercise_name' => 'Analytics Recruitment 2024',
                'pre_screen_criteria' => json_encode([
                    ['criterion' => 'Bachelor\'s degree in Statistics, Mathematics or related field', 'required' => true],
                    ['criterion' => 'Experience with SQL and data visualization tools', 'required' => true],
                    ['criterion' => 'Python or R programming skills', 'required' => true],
                    ['criterion' => 'Machine learning knowledge', 'required' => false]
                ]),
                'application_number' => 'APP-2024-003',
                'first_name' => 'Michael',
                'last_name' => 'Johnson',
                'created_at' => '2024-01-12 14:30:00',
                'recieved_acknowledged' => '2024-01-17 11:45:00',
                'pre_screened' => null,
                'pre_screened_status' => null,
                'pre_screened_remarks' => null
            ]
        ];

        return $applications[$id] ?? null;
    }

    /**
     * Generate mock experience data
     */
    private function getMockExperiences($applicationId)
    {
        $experiences = [
            1 => [
                [
                    'id' => 1,
                    'application_id' => 1,
                    'organization' => 'Tech Corp',
                    'position' => 'Software Developer',
                    'start_date' => '2020-01-15',
                    'end_date' => '2023-12-31',
                    'duties' => 'Developed web applications using React and Node.js',
                    'salary' => '$75,000',
                    'reason_for_leaving' => 'Career advancement'
                ],
                [
                    'id' => 2,
                    'application_id' => 1,
                    'organization' => 'StartupXYZ',
                    'position' => 'Junior Developer',
                    'start_date' => '2018-06-01',
                    'end_date' => '2019-12-31',
                    'duties' => 'Maintained legacy systems and developed new features',
                    'salary' => '$55,000',
                    'reason_for_leaving' => 'Company restructuring'
                ]
            ],
            2 => [
                [
                    'id' => 3,
                    'application_id' => 2,
                    'organization' => 'Innovation Inc',
                    'position' => 'Team Lead',
                    'start_date' => '2021-03-01',
                    'end_date' => null,
                    'duties' => 'Leading a team of 8 developers, project planning and execution',
                    'salary' => '$85,000',
                    'reason_for_leaving' => null
                ]
            ],
            3 => [
                [
                    'id' => 4,
                    'application_id' => 3,
                    'organization' => 'Data Solutions',
                    'position' => 'Data Analyst',
                    'start_date' => '2022-01-15',
                    'end_date' => null,
                    'duties' => 'Statistical analysis, report generation, data visualization',
                    'salary' => '$68,000',
                    'reason_for_leaving' => null
                ]
            ]
        ];

        return $experiences[$applicationId] ?? [];
    }

    /**
     * Generate mock education data
     */
    private function getMockEducation($applicationId)
    {
        $education = [
            1 => [
                [
                    'id' => 1,
                    'application_id' => 1,
                    'institution' => 'University of Technology',
                    'qualification' => 'Bachelor of Computer Science',
                    'start_year' => '2014',
                    'end_year' => '2018',
                    'grade' => 'First Class Honors',
                    'specialization' => 'Software Engineering'
                ],
                [
                    'id' => 2,
                    'application_id' => 1,
                    'institution' => 'Tech Institute',
                    'qualification' => 'Certificate in Web Development',
                    'start_year' => '2019',
                    'end_year' => '2019',
                    'grade' => 'A',
                    'specialization' => 'Full Stack Development'
                ]
            ],
            2 => [
                [
                    'id' => 3,
                    'application_id' => 2,
                    'institution' => 'Business University',
                    'qualification' => 'MBA in Project Management',
                    'start_year' => '2018',
                    'end_year' => '2020',
                    'grade' => 'Distinction',
                    'specialization' => 'Project Management'
                ]
            ],
            3 => [
                [
                    'id' => 4,
                    'application_id' => 3,
                    'institution' => 'State University',
                    'qualification' => 'Bachelor of Statistics',
                    'start_year' => '2016',
                    'end_year' => '2020',
                    'grade' => 'Second Class Upper',
                    'specialization' => 'Applied Statistics'
                ]
            ]
        ];

        return $education[$applicationId] ?? [];
    }

    /**
     * Generate mock files data
     */
    private function getMockFiles($applicationId)
    {
        $files = [
            1 => [
                [
                    'id' => 1,
                    'application_id' => 1,
                    'file_type' => 'resume',
                    'file_name' => 'john_doe_resume.pdf',
                    'file_path' => '/uploads/applications/1/john_doe_resume.pdf',
                    'file_size' => '2.5MB',
                    'uploaded_at' => '2024-01-10 08:15:00'
                ],
                [
                    'id' => 2,
                    'application_id' => 1,
                    'file_type' => 'cover_letter',
                    'file_name' => 'cover_letter.pdf',
                    'file_path' => '/uploads/applications/1/cover_letter.pdf',
                    'file_size' => '1.2MB',
                    'uploaded_at' => '2024-01-10 08:16:00'
                ]
            ],
            2 => [
                [
                    'id' => 3,
                    'application_id' => 2,
                    'file_type' => 'resume',
                    'file_name' => 'jane_smith_cv.pdf',
                    'file_path' => '/uploads/applications/2/jane_smith_cv.pdf',
                    'file_size' => '1.8MB',
                    'uploaded_at' => '2024-01-11 09:20:00'
                ]
            ],
            3 => [
                [
                    'id' => 4,
                    'application_id' => 3,
                    'file_type' => 'resume',
                    'file_name' => 'michael_johnson_resume.pdf',
                    'file_path' => '/uploads/applications/3/michael_johnson_resume.pdf',
                    'file_size' => '2.1MB',
                    'uploaded_at' => '2024-01-12 14:35:00'
                ]
            ]
        ];

        return $files[$applicationId] ?? [];
    }

    /**
     * [GET] Display the list of applications pending pre-screening.
     * URI: /application_pre_screening
     */
    public function index()
    {
        // Get mock applications data instead of database query
        $applications = $this->getMockApplications();

        $data = [
            'title' => 'Application Pre-Screening',
            'menu' => 'applications',
            'applications' => $applications
        ];

        return view('application_pre_screening/application_pre_screening_list', $data);
    }

    /**
     * [GET] Display the detailed view of a specific application for pre-screening.
     * URI: /application_pre_screening/show/{id}
     *
     * @param int $id Application ID
     * @return string|\CodeIgniter\HTTP\RedirectResponse
     */
    public function show($id)
    {
        // Get application with related position and exercise data
        $application = $this->getMockApplicationDetails($id);

        if (!$application) {
            return redirect()->to(base_url('application_pre_screening'))
                ->with('error', 'Application not found.');
        }

        // Fetch the corresponding applicant data
        $applicant = $this->getMockApplicant($application['applicant_id']);
        if (!$applicant) {
            // Handle case where applicant record is missing, though unlikely if application exists
            log_message('warning', 'Applicant record not found for application ID: ' . $id . ' and applicant ID: ' . $application['applicant_id']);
            $applicant = []; // Provide an empty array to avoid errors in the view
        }

        // Get related data for this application
        $experiences = $this->getMockExperiences($id);
        $education = $this->getMockEducation($id);
        $files = $this->getMockFiles($id);

        // Get position and exercise data
        $position = [
            'id' => $application['position_id'],
            'designation' => $application['designation'] ?? 'N/A'
        ];

        $exercise = [
            'id' => $application['exercise_id'],
            'exercise_name' => $application['exercise_name'] ?? 'N/A'
        ];

        // Parse pre-screening criteria
        $preScreenCriteria = [];
        if (!empty($application['pre_screen_criteria'])) {
            try {
                $preScreenCriteria = json_decode($application['pre_screen_criteria'], true) ?? [];
            } catch (\Exception $e) {
                log_message('error', 'Error parsing pre-screening criteria: ' . $e->getMessage());
            }
        }

        $data = [
            'title' => 'Application Pre-Screening',
            'menu' => 'applications',
            'application' => $application,
            'applicant' => $applicant, // Pass applicant data to the view
            'position' => $position,
            'exercise' => $exercise,
            'preScreenCriteria' => $preScreenCriteria,
            'experiences' => $experiences,
            'education' => $education,
            'files' => $files,
            'educationModel' => null // Mock educationModel placeholder
        ];

        return view('application_pre_screening/application_pre_screening_detailed_view', $data);
    }

    /**
     * [POST] Save pre-screening results for an application.
     * URI: /application_pre_screening/save/{id}
     *
     * @param int $id Application ID
     * @return \CodeIgniter\HTTP\RedirectResponse
     */
    public function save($id)
    {
        // Find the application first to ensure it exists
        $application = $this->getMockApplicationDetails($id);
        if (!$application) {
            return redirect()->to(base_url('application_pre_screening'))
                ->with('error', 'Application not found.');
        }

        // Validate input
        $rules = [
            'status' => 'required|in_list[passed,failed,pending]',
            'remarks' => 'permit_empty|string|max_length[1000]'
        ];

        if ($this->request->getPost('status') === 'failed' && empty($this->request->getPost('remarks'))) {
            $rules['remarks'] = 'required|string|max_length[1000]';
        }

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()
                ->with('errors', $this->validator->getErrors());
        }

        // Process criteria results
        $criteriaResults = [];
        $criteriaIndices = $this->request->getPost('criteria_index') ?? [];
        $criteriaMet = $this->request->getPost('criteria_met') ?? [];
        $criteriaRemarks = $this->request->getPost('criteria_remarks') ?? [];

        foreach ($criteriaIndices as $index => $criteriaIndex) {
            $criteriaResults[] = [
                'criteriaIndex' => $criteriaIndex,
                'met' => isset($criteriaMet[$criteriaIndex]) ? true : false,
                'remarks' => trim($criteriaRemarks[$criteriaIndex] ?? '')
            ];
        }

        // Prepare data for update (mock - not actually saving to database)
        $data = [
            'pre_screened' => date('Y-m-d H:i:s'),
            'pre_screened_by' => $this->session->get('user_id'),
            'pre_screened_status' => $this->request->getPost('status'),
            'pre_screened_remarks' => trim($this->request->getPost('remarks')),
            'pre_screened_criteria_results' => json_encode($criteriaResults),
            'updated_by' => $this->session->get('user_id')
        ];

        // Mock successful update
        log_message('info', 'Mock pre-screening save for application ID: ' . $id . ' with data: ' . json_encode($data));

        return redirect()->to(base_url('application_pre_screening/show/' . $id))
            ->with('success', 'Pre-screening results saved successfully (mock mode).');
    }

    /**
     * [POST] Batch pre-screen multiple applications.
     * URI: /application_pre_screening/batch_update
     */
    public function batchUpdate()
    {
        $ids = $this->request->getPost('ids');
        $status = $this->request->getPost('status');
        $remarks = trim($this->request->getPost('remarks') ?? '');

        // --- Validation ---
        if (empty($ids) || !is_array($ids)) {
            $this->session->setFlashdata('error', 'No applications selected for batch update.');
            return redirect()->back();
        }

        $allowed_statuses = ['passed', 'failed', 'pending'];
        if (empty($status) || !in_array($status, $allowed_statuses)) {
            $this->session->setFlashdata('error', 'Invalid or missing status for batch update.');
            return redirect()->back()->withInput();
        }

        // Require remarks if status is 'failed'
        if ($status === 'failed' && empty($remarks)) {
             $this->session->setFlashdata('error', 'Remarks are required when batch failing applications.');
            return redirect()->back()->withInput();
        }

        // --- Mock batch update ---
        $data = [
            'pre_screened' => date('Y-m-d H:i:s'),
            'pre_screened_by' => $this->session->get('user_id'),
            'pre_screened_status' => $status,
            'pre_screened_remarks' => "[Batch Update] " . $remarks,
            'pre_screened_criteria_results' => null,
            'updated_at' => date('Y-m-d H:i:s'),
            'updated_by' => $this->session->get('user_id')
        ];

        $successCount = 0;
        $failCount = 0;

        foreach ($ids as $id) {
            $id = (int) $id;
            if ($id > 0) {
                try {
                    // Mock successful update
                    $successCount++;
                    log_message('info', 'Mock batch pre-screen for ID: ' . $id . ' with status: ' . $status);
                } catch (\Exception $e) {
                    log_message('error', 'Exception during batch pre-screening for ID ' . $id . ': ' . $e->getMessage());
                    $failCount++;
                }
            } else {
                $failCount++;
            }
        }

        // --- Set Flash Message ---
        $message = '';
        if ($successCount > 0) {
            $message .= "$successCount application(s) pre-screened successfully (mock mode). ";
        }
        if ($failCount > 0) {
            $message .= "$failCount application(s) failed to update.";
        }

        if ($successCount > 0 && $failCount == 0) {
            $this->session->setFlashdata('success', $message);
        } elseif ($failCount > 0) {
            $this->session->setFlashdata('warning', $message);
        } else {
             $this->session->setFlashdata('info', 'No applications were updated.');
        }

        return redirect()->to(base_url('application_pre_screening'));
    }

    /**
     * [GET] Display exercises in selection status for pre-screening context.
     * URI: /application_pre_screening/exercises
     */
    public function exercises()
    {
        $orgId = $this->session->get('org_id');
        if (!$orgId) {
            $this->session->setFlashdata('error', 'Organization context not found.');
            return redirect()->to(base_url());
        }

        $exercises = [
            [
                'id' => 1,
                'exercise_name' => 'IT Recruitment Exercise 2024',
                'advertisement_no' => 'ADV-2024-001',
                'exercise_year' => '2024',
                'department' => 'Information Technology',
                'status' => 'selection',
                'application_count' => 25,
                'created_at' => '2024-01-01 08:00:00'
            ],
            [
                'id' => 2,
                'exercise_name' => 'Finance Department Recruitment 2024',
                'advertisement_no' => 'ADV-2024-002',
                'exercise_year' => '2024',
                'department' => 'Finance',
                'status' => 'selection',
                'application_count' => 18,
                'created_at' => '2024-01-05 09:00:00'
            ]
        ];

        $data = [
            'title' => 'Exercises Available for Pre-Screening',
            'menu' => 'applications',
            'exercises' => $exercises
        ];

        return view('application_pre_screening/exercise_list', $data);
    }

    /**
     * [GET] Display position groups within an exercise.
     * URI: /application_pre_screening/exercise/{exerciseId}/position_groups
     */
    public function viewPositionGroups($exerciseId)
    {
        $exercise = [
            'id' => $exerciseId,
            'exercise_name' => 'IT Recruitment Exercise 2024',
            'advertisement_no' => 'ADV-2024-001',
            'exercise_year' => '2024',
            'department' => 'Information Technology',
            'status' => 'selection',
            'description' => 'Annual IT recruitment exercise for various technical positions'
        ];

        $positionGroups = [
            [
                'id' => 1,
                'group_name' => 'Technology',
                'exercise_id' => $exerciseId,
                'positions_count' => 10,
                'applications_count' => 10,
                'pending_count' => 0
            ],
            [
                'id' => 2,
                'group_name' => 'Management',
                'exercise_id' => $exerciseId,
                'positions_count' => 5,
                'applications_count' => 5,
                'pending_count' => 0
            ],
            [
                'id' => 3,
                'group_name' => 'Analytics',
                'exercise_id' => $exerciseId,
                'positions_count' => 5,
                'applications_count' => 5,
                'pending_count' => 0
            ]
        ];

        $data = [
            'title' => 'Position Groups in Exercise: ' . esc($exercise['exercise_name']),
            'menu' => 'applications',
            'exercise' => $exercise,
            'positionGroups' => $positionGroups
        ];

        return view('application_pre_screening/position_groups', $data);
    }

    /**
     * [GET] Display positions within a position group.
     * URI: /application_pre_screening/position_group/{groupId}/positions
     */
    public function viewPositions($groupId)
    {
        $positionGroup = [
            'id' => $groupId,
            'group_name' => 'Technology',
            'exercise_id' => 1
        ];

        $positions = [
            [
                'id' => 1,
                'designation' => 'Senior Software Engineer',
                'position_group_id' => $groupId,
                'applications_count' => 10,
                'pending_count' => 0
            ],
            [
                'id' => 2,
                'designation' => 'Project Manager',
                'position_group_id' => $groupId,
                'applications_count' => 5,
                'pending_count' => 0
            ],
            [
                'id' => 3,
                'designation' => 'Data Analyst',
                'position_group_id' => $groupId,
                'applications_count' => 5,
                'pending_count' => 0
            ]
        ];

        $data = [
            'title' => 'Positions in Group: ' . esc($positionGroup['group_name']),
            'menu' => 'applications',
            'positionGroup' => $positionGroup,
            'positions' => $positions
        ];

        return view('application_pre_screening/positions_in_group', $data);
    }

    /**
     * [GET] Display all applications for a specific exercise that need pre-screening.
     * URI: /application_pre_screening/exercise/{exerciseId}/applications
     */
    public function exerciseApplications($exerciseId)
    {
        $exercise = [
            'id' => $exerciseId,
            'exercise_name' => 'IT Recruitment Exercise 2024',
            'advertisement_no' => 'ADV-2024-001',
            'exercise_year' => '2024',
            'department' => 'Information Technology',
            'status' => 'selection',
            'application_count' => 25,
            'created_at' => '2024-01-01 08:00:00'
        ];

        $positionsWithApplications = [
            [
                'position_id' => 1,
                'total_application_count' => 10,
                'pending_prescreen_count' => 0,
                'position_name' => 'Senior Software Engineer',
                'position_group_id' => 1,
                'position_group_name' => 'Technology'
            ],
            [
                'position_id' => 2,
                'total_application_count' => 5,
                'pending_prescreen_count' => 0,
                'position_name' => 'Project Manager',
                'position_group_id' => 1,
                'position_group_name' => 'Technology'
            ],
            [
                'position_id' => 3,
                'total_application_count' => 5,
                'pending_prescreen_count' => 0,
                'position_name' => 'Data Analyst',
                'position_group_id' => 1,
                'position_group_name' => 'Technology'
            ]
        ];

        $data = [
            'title' => 'Exercise Positions for Pre-Screening: ' . esc($exercise['exercise_name']),
            'menu' => 'applications',
            'exercise' => $exercise,
            'positionsData' => $positionsWithApplications
        ];

        return view('application_pre_screening/exercise_applications', $data);
    }

    /**
     * [GET] Display positions within a position group for pre-screening.
     * URI: /application_pre_screening/position_group/{groupId}/positions
     */
    public function positionGroupPositions($groupId)
    {
        // Mock position group data based on PositionsModel structure
        $positionGroup = [
            'id' => $groupId,
            'group_name' => $groupId == 1 ? 'Technology' : ($groupId == 2 ? 'Management' : 'Analytics'),
            'exercise_id' => 1,
            'description' => 'Position group for ' . ($groupId == 1 ? 'Technology' : ($groupId == 2 ? 'Management' : 'Analytics')) . ' positions',
            'created_at' => '2024-01-01 08:00:00'
        ];

        // Mock exercise data based on ExerciseModel structure
        $exercise = [
            'id' => 1,
            'org_id' => 1,
            'exercise_name' => 'IT Recruitment Exercise 2024',
            'gazzetted_no' => 'GAZ-2024-001',
            'gazzetted_date' => '2024-01-01',
            'advertisement_no' => 'ADV-2024-001',
            'advertisement_date' => '2024-01-05',
            'mode_of_advertisement' => 'Online and Print Media',
            'publish_date_from' => '2024-01-10',
            'publish_date_to' => '2024-02-10',
            'description' => 'Annual IT recruitment exercise for various technical positions',
            'status' => 'selection',
            'created_at' => '2024-01-01 08:00:00'
        ];

        // Mock positions data based on PositionsModel structure
        $positions = [];
        if ($groupId == 1) { // Technology group
            $positions = [
                [
                    'id' => 1,
                    'exercise_id' => 1,
                    'org_id' => 1,
                    'position_group_id' => $groupId,
                    'position_reference' => 'IT-SR-001',
                    'designation' => 'Senior Software Engineer',
                    'classification' => 'Professional',
                    'award' => 'IT Professional Award Level 3',
                    'location' => 'Port Moresby, NCD',
                    'annual_salary' => 'K65,000 - K85,000',
                    'qualifications' => 'Bachelor\'s degree in Computer Science or related field, minimum 5 years experience',
                    'knowledge' => 'Advanced programming languages, database management, system architecture',
                    'skills_competencies' => 'Leadership, project management, technical problem solving',
                    'job_experiences' => 'Minimum 5 years in software development, team leadership experience preferred',
                    'status' => 'active',
                    'applications_count' => 12,
                    'pending_prescreen_count' => 3,
                    'created_at' => '2024-01-01 08:00:00'
                ],
                [
                    'id' => 2,
                    'exercise_id' => 1,
                    'org_id' => 1,
                    'position_group_id' => $groupId,
                    'position_reference' => 'IT-PM-002',
                    'designation' => 'IT Project Manager',
                    'classification' => 'Management',
                    'award' => 'Management Award Level 2',
                    'location' => 'Port Moresby, NCD',
                    'annual_salary' => 'K70,000 - K90,000',
                    'qualifications' => 'Bachelor\'s degree in IT/Business, PMP certification preferred, minimum 7 years experience',
                    'knowledge' => 'Project management methodologies, IT systems, stakeholder management',
                    'skills_competencies' => 'Leadership, communication, strategic planning, risk management',
                    'job_experiences' => 'Minimum 7 years in project management, IT project experience required',
                    'status' => 'active',
                    'applications_count' => 8,
                    'pending_prescreen_count' => 2,
                    'created_at' => '2024-01-01 08:00:00'
                ],
                [
                    'id' => 3,
                    'exercise_id' => 1,
                    'org_id' => 1,
                    'position_group_id' => $groupId,
                    'position_reference' => 'IT-DA-003',
                    'designation' => 'Data Analyst',
                    'classification' => 'Professional',
                    'award' => 'Data Professional Award Level 2',
                    'location' => 'Port Moresby, NCD',
                    'annual_salary' => 'K55,000 - K75,000',
                    'qualifications' => 'Bachelor\'s degree in Statistics, Mathematics, Computer Science or related field',
                    'knowledge' => 'Statistical analysis, data visualization, SQL, Python/R programming',
                    'skills_competencies' => 'Analytical thinking, attention to detail, communication',
                    'job_experiences' => 'Minimum 3 years in data analysis, experience with BI tools preferred',
                    'status' => 'active',
                    'applications_count' => 6,
                    'pending_prescreen_count' => 1,
                    'created_at' => '2024-01-01 08:00:00'
                ]
            ];
        } elseif ($groupId == 2) { // Management group
            $positions = [
                [
                    'id' => 4,
                    'exercise_id' => 1,
                    'org_id' => 1,
                    'position_group_id' => $groupId,
                    'position_reference' => 'MG-DM-004',
                    'designation' => 'Department Manager',
                    'classification' => 'Executive',
                    'award' => 'Executive Award Level 1',
                    'location' => 'Port Moresby, NCD',
                    'annual_salary' => 'K90,000 - K120,000',
                    'qualifications' => 'Master\'s degree in Management/Business Administration, minimum 10 years experience',
                    'knowledge' => 'Strategic planning, organizational management, financial planning',
                    'skills_competencies' => 'Leadership, decision making, strategic thinking, team management',
                    'job_experiences' => 'Minimum 10 years in management roles, department leadership experience',
                    'status' => 'active',
                    'applications_count' => 5,
                    'pending_prescreen_count' => 1,
                    'created_at' => '2024-01-01 08:00:00'
                ]
            ];
        } else { // Analytics group
            $positions = [
                [
                    'id' => 5,
                    'exercise_id' => 1,
                    'org_id' => 1,
                    'position_group_id' => $groupId,
                    'position_reference' => 'AN-BA-005',
                    'designation' => 'Business Analyst',
                    'classification' => 'Professional',
                    'award' => 'Business Professional Award Level 2',
                    'location' => 'Port Moresby, NCD',
                    'annual_salary' => 'K60,000 - K80,000',
                    'qualifications' => 'Bachelor\'s degree in Business, Economics or related field',
                    'knowledge' => 'Business process analysis, requirements gathering, stakeholder management',
                    'skills_competencies' => 'Analytical thinking, communication, problem solving',
                    'job_experiences' => 'Minimum 4 years in business analysis or related field',
                    'status' => 'active',
                    'applications_count' => 4,
                    'pending_prescreen_count' => 1,
                    'created_at' => '2024-01-01 08:00:00'
                ]
            ];
        }

        $data = [
            'title' => 'Positions in Group: ' . esc($positionGroup['group_name']),
            'menu' => 'applications',
            'positionGroup' => $positionGroup,
            'exercise' => $exercise,
            'positions' => $positions
        ];

        return view('application_pre_screening/application_pre_screening_position_group_positions', $data);
    }

    /**
     * [GET] Display all applications for a specific position that need pre-screening.
     * URI: /application_pre_screening/position/{positionId}/applications
     */
    public function positionApplications($positionId)
    {
        $position = [
            'id' => $positionId,
            'designation' => 'Senior Software Engineer',
            'position_group_id' => 1,
            'group_name' => 'Technology',
            'exercise_id' => 1,
            'exercise_name' => 'IT Recruitment Exercise 2024',
            'classification' => 'Technical',
            'location' => 'Remote/HQ',
            'annual_salary' => '$80,000 - $120,000'
        ];

        $positionGroup = [
            'id' => 1,
            'group_name' => 'Technology'
        ];

        $exercise = [
            'id' => 1,
            'exercise_name' => 'IT Recruitment Exercise 2024'
        ];

        $applications = [
            [
                'id' => 1,
                'applicant_id' => 101,
                'application_number' => 'APP-2024-001',
                'recieved_acknowledged' => '2024-01-15 10:30:00',
                'pre_screened' => null,
                'pre_screened_status' => null,
                'pre_screened_remarks' => null,
                'created_at' => '2024-01-10 08:00:00',
                'fname' => 'John',
                'lname' => 'Doe',
                'first_name' => 'John',
                'last_name' => 'Doe'
            ],
            [
                'id' => 5,
                'applicant_id' => 105,
                'application_number' => 'APP-2024-005',
                'recieved_acknowledged' => '2024-01-19 12:15:00',
                'pre_screened' => null,
                'pre_screened_status' => null,
                'pre_screened_remarks' => null,
                'created_at' => '2024-01-14 11:20:00',
                'fname' => 'Alex',
                'lname' => 'Brown',
                'first_name' => 'Alex',
                'last_name' => 'Brown'
            ]
        ];

        $data = [
            'title' => 'Applications for Position: ' . esc($position['designation']),
            'menu' => 'applications',
            'position' => $position,
            'positionGroup' => $positionGroup,
            'exercise' => $exercise,
            'applications' => $applications
        ];

        return view('application_pre_screening/position_applications', $data);
    }

    /**
     * [GET] Display all acknowledged applications for pre-screening regardless of exercise/position.
     * URI: /application_pre_screening/all_acknowledged
     */
    public function allAcknowledgedApplications()
    {
        $applications = $this->getMockApplications();

        $data = [
            'title' => 'All Acknowledged Applications for Pre-Screening',
            'menu' => 'applications',
            'applications' => $applications
        ];

        return view('application_pre_screening/all_acknowledged_applications', $data);
    }

    /**
     * [GET] Display the exercise list for pre-screening.
     * URI: /application_pre_screening/exercise_list or /application_pre_screening/exercise_list/{id}
     */
    public function exerciseList($id = null)
    {
        if ($id !== null) {
            // Display specific exercise details
            $exercise = [
                'id' => $id,
                'exercise_name' => 'IT Recruitment Exercise 2024',
                'advertisement_no' => 'ADV-2024-001',
                'exercise_year' => '2024',
                'department' => 'Information Technology',
                'status' => 'selection',
                'description' => 'Annual IT recruitment exercise for various technical positions',
                'positions_count' => 15,
                'applications_count' => 25,
                'pending_prescreen_count' => 8,
                'passed_prescreen_count' => 12,
                'failed_prescreen_count' => 5
            ];

            $positionGroups = [
                [
                    'id' => 1,
                    'group_name' => 'Technology',
                    'exercise_id' => $id,
                    'positions_count' => 10,
                    'applications_count' => 15,
                    'pending_count' => 5,
                    'passed_count' => 8,
                    'failed_count' => 2
                ],
                [
                    'id' => 2,
                    'group_name' => 'Management',
                    'exercise_id' => $id,
                    'positions_count' => 3,
                    'applications_count' => 7,
                    'pending_count' => 2,
                    'passed_count' => 3,
                    'failed_count' => 2
                ],
                [
                    'id' => 3,
                    'group_name' => 'Analytics',
                    'exercise_id' => $id,
                    'positions_count' => 2,
                    'applications_count' => 3,
                    'pending_count' => 1,
                    'passed_count' => 1,
                    'failed_count' => 1
                ]
            ];

            $data = [
                'title' => 'Exercise Details: ' . esc($exercise['exercise_name']),
                'menu' => 'applications',
                'exercise' => $exercise,
                'positionGroups' => $positionGroups
            ];

            return view('application_pre_screening/exercise_details', $data);
        } else {
            // Display list of exercises
            $exercises = [
                [
                    'id' => 1,
                    'exercise_name' => 'IT Recruitment Exercise 2024',
                    'advertisement_no' => 'ADV-2024-001',
                    'exercise_year' => '2024',
                    'department' => 'Information Technology',
                    'status' => 'selection',
                    'application_count' => 25,
                    'pending_prescreen_count' => 8,
                    'passed_prescreen_count' => 12,
                    'failed_prescreen_count' => 5,
                    'created_at' => '2024-01-01 08:00:00'
                ],
                [
                    'id' => 2,
                    'exercise_name' => 'Finance Department Recruitment 2024',
                    'advertisement_no' => 'ADV-2024-002',
                    'exercise_year' => '2024',
                    'department' => 'Finance',
                    'status' => 'selection',
                    'application_count' => 18,
                    'pending_prescreen_count' => 6,
                    'passed_prescreen_count' => 10,
                    'failed_prescreen_count' => 2,
                    'created_at' => '2024-01-05 09:00:00'
                ],
                [
                    'id' => 3,
                    'exercise_name' => 'HR Recruitment Exercise 2024',
                    'advertisement_no' => 'ADV-2024-003',
                    'exercise_year' => '2024',
                    'department' => 'Human Resources',
                    'status' => 'selection',
                    'application_count' => 12,
                    'pending_prescreen_count' => 4,
                    'passed_prescreen_count' => 6,
                    'failed_prescreen_count' => 2,
                    'created_at' => '2024-01-10 10:00:00'
                ]
            ];

            $data = [
                'title' => 'Exercise List for Pre-Screening',
                'menu' => 'applications',
                'exercises' => $exercises
            ];

            return view('application_pre_screening/exercise_list_view', $data);
        }
    }

    /**
     * [GET] View a specific application (alternative to show method).
     * URI: /application_pre_screening/view_application/{id}
     */
    public function viewApplication($id)
    {
        $application = $this->getMockApplicationDetails($id);

        if (!$application) {
            return redirect()->to(base_url('application_pre_screening'))
                ->with('error', 'Application not found.');
        }

        $applicant = $this->getMockApplicant($application['applicant_id']);
        $experiences = $this->getMockExperiences($id);
        $education = $this->getMockEducation($id);
        $files = $this->getMockFiles($id);

        // Parse pre-screening criteria
        $preScreenCriteria = [];
        if (!empty($application['pre_screen_criteria'])) {
            try {
                $preScreenCriteria = json_decode($application['pre_screen_criteria'], true) ?? [];
            } catch (\Exception $e) {
                log_message('error', 'Error parsing pre-screening criteria: ' . $e->getMessage());
            }
        }

        $data = [
            'title' => 'View Application: ' . esc($application['application_number']),
            'menu' => 'applications',
            'application' => $application,
            'applicant' => $applicant,
            'preScreenCriteria' => $preScreenCriteria,
            'experiences' => $experiences,
            'education' => $education,
            'files' => $files
        ];

        return view('application_pre_screening/view_application', $data);
    }
}