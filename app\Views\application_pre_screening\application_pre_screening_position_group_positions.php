<?php
/**
 * View file for listing positions within a position group for pre-screening
 *
 * @var array $positionGroup Position group details
 * @var array $exercise Exercise details
 * @var array $positions List of positions in the group
 */
?>

<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Breadcrumb Navigation -->
    <div class="row mb-3">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="<?= base_url('application_pre_screening/exercises') ?>">Exercises</a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="<?= base_url('application_pre_screening/exercise/' . $exercise['id'] . '/position_groups') ?>">
                            <?= esc($exercise['exercise_name']) ?>
                        </a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">
                        <?= esc($positionGroup['group_name']) ?> Positions
                    </li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Header Section -->
    <div class="row mb-3">
        <div class="col-md-8">
            <h2><i class="fas fa-briefcase me-2"></i>Positions in Group</h2>
            <p class="text-muted">
                Position Group: <strong><?= esc($positionGroup['group_name']) ?></strong><br>
                <small>Exercise: <?= esc($exercise['exercise_name']) ?> | Advertisement No: <?= esc($exercise['advertisement_no']) ?></small>
            </p>
        </div>
        <div class="col-md-4 text-end">
            <a href="<?= base_url('application_pre_screening/exercise/' . $exercise['id'] . '/position_groups') ?>" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-1"></i> Back to Position Groups
            </a>
        </div>
    </div>

    <!-- Exercise and Position Group Details Card -->
    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">
                <i class="fas fa-info-circle me-2"></i>Group Details
            </h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-3">
                    <strong>Exercise Name:</strong><br>
                    <?= esc($exercise['exercise_name']) ?>
                </div>
                <div class="col-md-3">
                    <strong>Position Group:</strong><br>
                    <?= esc($positionGroup['group_name']) ?>
                </div>
                <div class="col-md-3">
                    <strong>Total Positions:</strong><br>
                    <span class="badge bg-primary fs-6"><?= count($positions) ?></span>
                </div>
                <div class="col-md-3">
                    <strong>Total Applications:</strong><br>
                    <span class="badge bg-info fs-6">
                        <?= array_sum(array_column($positions, 'applications_count')) ?>
                    </span>
                </div>
            </div>
        </div>
    </div>

    <!-- Positions List -->
    <div class="card">
        <div class="card-header bg-white py-3">
            <h5 class="mb-0">
                <i class="fas fa-list me-2"></i>Available Positions
            </h5>
        </div>
        <div class="card-body">
            <?php if (empty($positions)): ?>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>No positions found in this group.
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table id="positionsTable" class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th width="5%">#</th>
                                <th width="15%">Reference</th>
                                <th width="25%">Position Title</th>
                                <th width="15%">Classification</th>
                                <th width="15%">Location</th>
                                <th width="10%">Applications</th>
                                <th width="10%">Pending</th>
                                <th width="15%">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $count = 1; ?>
                            <?php foreach ($positions as $position): ?>
                                <tr>
                                    <td><?= $count++ ?></td>
                                    <td>
                                        <code><?= esc($position['position_reference']) ?></code>
                                    </td>
                                    <td>
                                        <strong><?= esc($position['designation']) ?></strong><br>
                                        <small class="text-muted"><?= esc($position['award']) ?></small>
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary"><?= esc($position['classification']) ?></span>
                                    </td>
                                    <td>
                                        <i class="fas fa-map-marker-alt me-1"></i>
                                        <?= esc($position['location']) ?>
                                    </td>
                                    <td>
                                        <span class="badge bg-primary"><?= $position['applications_count'] ?></span>
                                    </td>
                                    <td>
                                        <?php if ($position['pending_prescreen_count'] > 0): ?>
                                            <span class="badge bg-warning"><?= $position['pending_prescreen_count'] ?></span>
                                        <?php else: ?>
                                            <span class="badge bg-success">0</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <?php if ($position['applications_count'] > 0): ?>
                                                <a href="<?= base_url('application_pre_screening/position_applications/' . $position['id']) ?>"
                                                   class="btn btn-sm btn-primary"
                                                   data-bs-toggle="tooltip"
                                                   title="View Applications">
                                                    <i class="fas fa-list"></i>
                                                </a>
                                                <?php if ($position['pending_prescreen_count'] > 0): ?>
                                                    <a href="<?= base_url('application_pre_screening/position_applications/' . $position['id']) ?>"
                                                       class="btn btn-sm btn-success"
                                                       data-bs-toggle="tooltip"
                                                       title="Pre-Screen Applications">
                                                        <i class="fas fa-clipboard-check"></i>
                                                    </a>
                                                <?php endif; ?>
                                            <?php else: ?>
                                                <button class="btn btn-sm btn-outline-secondary" disabled>
                                                    <i class="fas fa-inbox"></i> No Applications
                                                </button>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    // Initialize DataTable
    $('#positionsTable').DataTable({
        responsive: true,
        order: [[0, 'asc']], // Sort by row number
        language: {
            search: "Search positions:",
            lengthMenu: "Show _MENU_ positions per page",
            info: "Showing _START_ to _END_ of _TOTAL_ positions",
            emptyTable: "No positions available in this group",
        }
    });

    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    console.log('Position Group Positions view loaded');
});
</script>
<?= $this->endSection() ?>
