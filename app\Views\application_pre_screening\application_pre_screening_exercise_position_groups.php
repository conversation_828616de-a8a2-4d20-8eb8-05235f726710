<?php
/**
 * View file for listing position groups within an exercise for pre-screening
 * 
 * @var array $exercise Exercise details
 * @var array $positionGroups List of position groups in the exercise
 */
?>

<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Breadcrumb Navigation -->
    <div class="row mb-3">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="<?= base_url('application_pre_screening/exercises') ?>">Exercises</a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">
                        <?= esc($exercise['exercise_name']) ?>
                    </li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Header Section -->
    <div class="row mb-3">
        <div class="col-md-8">
            <h2><i class="fas fa-object-group me-2"></i>Position Groups</h2>
            <p class="text-muted">
                Position groups for exercise: <strong><?= esc($exercise['exercise_name']) ?></strong><br>
                <small>Advertisement No: <?= esc($exercise['advertisement_no']) ?> | Gazetted No: <?= esc($exercise['gazzetted_no']) ?></small>
            </p>
        </div>
        <div class="col-md-4 text-end">
            <a href="<?= base_url('application_pre_screening/exercises') ?>" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-1"></i> Back to Exercises
            </a>
        </div>
    </div>

    <!-- Exercise Details Card -->
    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">
                <i class="fas fa-info-circle me-2"></i>Exercise Details
            </h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-3">
                    <strong>Exercise Name:</strong><br>
                    <?= esc($exercise['exercise_name']) ?>
                </div>
                <div class="col-md-3">
                    <strong>Advertisement No:</strong><br>
                    <?= esc($exercise['advertisement_no']) ?>
                </div>
                <div class="col-md-3">
                    <strong>Publication Period:</strong><br>
                    <?= date('d M Y', strtotime($exercise['publish_date_from'])) ?> - 
                    <?= date('d M Y', strtotime($exercise['publish_date_to'])) ?>
                </div>
                <div class="col-md-3">
                    <strong>Status:</strong><br>
                    <span class="badge bg-success fs-6"><?= ucfirst(esc($exercise['status'])) ?></span>
                </div>
            </div>
        </div>
    </div>

    <!-- Position Groups -->
    <div class="card">
        <div class="card-header bg-white py-3">
            <h5 class="mb-0">
                <i class="fas fa-layer-group me-2"></i>Position Groups
            </h5>
        </div>
        <div class="card-body">
            <?php if (empty($positionGroups)): ?>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>No position groups found for this exercise.
                </div>
            <?php else: ?>
                <div class="row">
                    <?php foreach ($positionGroups as $group): ?>
                        <div class="col-md-6 col-lg-4 mb-4">
                            <div class="card hover-card h-100">
                                <div class="card-body text-center">
                                    <div class="d-inline-block p-3 rounded-circle bg-primary text-white mb-3">
                                        <i class="fas fa-users fa-2x"></i>
                                    </div>
                                    <h5 class="card-title"><?= esc($group['group_name']) ?></h5>
                                    <p class="text-muted small"><?= esc($group['description']) ?></p>
                                    
                                    <!-- Statistics Row -->
                                    <div class="row text-center mt-3">
                                        <div class="col-6">
                                            <div class="border-end">
                                                <h4 class="text-primary mb-0"><?= $group['positions_count'] ?></h4>
                                                <small class="text-muted">Positions</small>
                                            </div>
                                        </div>
                                        <div class="col-6">
                                            <h4 class="text-info mb-0"><?= $group['applications_count'] ?></h4>
                                            <small class="text-muted">Applications</small>
                                        </div>
                                    </div>

                                    <!-- Pre-screening Status -->
                                    <div class="row text-center mt-2">
                                        <div class="col-4">
                                            <span class="badge bg-warning"><?= $group['pending_prescreen_count'] ?></span><br>
                                            <small class="text-muted">Pending</small>
                                        </div>
                                        <div class="col-4">
                                            <span class="badge bg-success"><?= $group['passed_prescreen_count'] ?></span><br>
                                            <small class="text-muted">Passed</small>
                                        </div>
                                        <div class="col-4">
                                            <span class="badge bg-danger"><?= $group['failed_prescreen_count'] ?></span><br>
                                            <small class="text-muted">Failed</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-footer bg-light">
                                    <div class="d-grid gap-2">
                                        <a href="<?= base_url('application_pre_screening/position_group/' . $group['id'] . '/positions') ?>" 
                                           class="btn btn-primary">
                                            <i class="fas fa-briefcase me-1"></i> View Positions
                                        </a>
                                        <?php if ($group['pending_prescreen_count'] > 0): ?>
                                            <a href="<?= base_url('application_pre_screening/position_group/' . $group['id'] . '/positions') ?>" 
                                               class="btn btn-outline-warning">
                                                <i class="fas fa-clipboard-check me-1"></i> Pre-Screen Applications
                                            </a>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>

                <!-- Summary Statistics -->
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card bg-light">
                            <div class="card-body">
                                <h6 class="card-title">
                                    <i class="fas fa-chart-bar me-2"></i>Exercise Summary
                                </h6>
                                <div class="row text-center">
                                    <div class="col-md-3">
                                        <h4 class="text-primary"><?= count($positionGroups) ?></h4>
                                        <small class="text-muted">Position Groups</small>
                                    </div>
                                    <div class="col-md-3">
                                        <h4 class="text-info"><?= array_sum(array_column($positionGroups, 'positions_count')) ?></h4>
                                        <small class="text-muted">Total Positions</small>
                                    </div>
                                    <div class="col-md-3">
                                        <h4 class="text-success"><?= array_sum(array_column($positionGroups, 'applications_count')) ?></h4>
                                        <small class="text-muted">Total Applications</small>
                                    </div>
                                    <div class="col-md-3">
                                        <h4 class="text-warning"><?= array_sum(array_column($positionGroups, 'pending_prescreen_count')) ?></h4>
                                        <small class="text-muted">Pending Pre-Screening</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    // Initialize tooltips if any
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    console.log('Exercise Position Groups view loaded');
});
</script>
<?= $this->endSection() ?>
